package contextutils

import (
	"sync"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

// Gets populated at resource context initialization and on any additional finds
var (
	GlobalValuesMutex                   sync.Mutex
	DefaultAppValues, DefaultTeamValues []string
	AppValues                           = make(map[string][]string)
	TeamValues                          = make(map[string][]string)
	AIDetectedApps                      = make(map[string]string)
	AIRejectedTeamValues                = make(map[string]struct{})
	AIRejectedAppValues                 = make(map[string]struct{})
)

func init() {

	AppValues = map[string][]string{
		// Common Apps
		JENKINS_APP:              {`jenkins`},
		ARGO_APP:                 {`argo[-_\s]*cd`},
		TERRAFORM_APP:            {`terraform`, `\btf\b`, `terragrunt`, `azdotf`},
		CICD_APP:                 {`\bcicd\b`},
		GITHUB_APP:               {`github`},
		GITLAB_APP:               {`gitlab`},
		BITBUCKET_APP:            {`bitbucket`},
		"Wiz":                    {`\bwiz\b`},
		"Orca":                   {`\borca\b`},
		"Palo Alto Prisma Cloud": {`palo[-_\s]*alto`, `prisma[-_\s]*cloud`},
		"Lacework":               {`lacework`},
		"Qualys":                 {`qualys`},
		"JFrog":                  {`jfrog`},
		"Okta":                   {`\bokta\b`},
		"Ping Identity":          {`ping[-_\s]*identity`},
		"ELK":                    {`\belk\b`},
		"Logstash":               {`logstash`},
		"Kibana":                 {`kibana`},
		"Splunk":                 {`splunk`},
		"Confluence":             {`confluence`},
		"Jira":                   {`\bjira\b`},
		"Akeyless":               {`akeyless`},
	}

	TeamValues = map[string][]string{
		// Common Teams
		ENGINEERING_TEAM:        {`engineering`},
		DEVOPS_TEAM:             {`dev[-_\s]*ops`},
		OPERATIONS_TEAM:         {`operations`, `\bops\b`},
		PRODUCT_MANAGEMENT_TEAM: {`product[-_\s]*management`},
		SECURITY_TEAM:           {`security`, `info[-_\s]*sec`},
		SALES_TEAM:              {`\bsales\b`},
		MARKETING_TEAM:          {`marketing`},
		CUSTOMER_SUPPORT_TEAM:   {`customer[-_\s]*support`, `customer[-_\s]*success`},
		HR_TEAM:                 {`human[-_\s]*resources`, `\bhr\b`},
		FINANCE_TEAM:            {`finance`, `accounting`, `billing`},
		COST_TEAM:               {`cost[-_\s]*optimization`, `cost[-_\s]*team`},
		DATA_TEAM:               {`data[-_\s]*team`, `data[-_\s]*engineering`, `data[-_\s]*analytics`},
		SRE_TEAM:                {`\bsre\b`, `site[-_\s]*reliability[-_\s]*engineering`},
		PLATFORM_TEAM:           {`platform[-_\s]*engineering`, `platform[-_\s]*team`},
		INFRA_TEAM:              {`infrastructure`, `\binfra\b`},
		NETWORKING_TEAM:         {`networking`},
		SOLUTIONS_TEAM:          {`solutions`},
		COMPLIANCE_TEAM:         {`compliance`},
		GOVERNANCE_TEAM:         {`governance`},
		// DEVELOPMENT_TEAM:        {`development`},
		// DESIGN_UXUI_TEAM:        {`design`, `\bux\b`, `\bui\b`, `user[-_\s]*interface`, `user[-_\s]*experience`},
		// QA_TEAM:                 {`quality[-_\s]*assurance`, `testing`, `\bqa\b`},
	}
}

func GetCompanyName(tenantID string) (string, error) {

	tenantQuery := `{"query":{"bool":{"filter":[{"match":{"id.keyword":"` + tenantID + `"}}]}}}`

	tenantDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TENANTS_INDEX}, tenantQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Error fetching tenant doc", []string{tenantID}, err)
		return "", err
	}

	if len(tenantDocs) > 0 {
		for _, tenantDoc := range tenantDocs {
			if name, ok := tenantDoc["companyName"].(string); ok {
				return name, nil
			}
		}
	}

	return "", err
}
