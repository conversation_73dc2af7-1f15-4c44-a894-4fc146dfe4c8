package openai

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/precize/logger"
	"github.com/precize/transport"
)

const baseURL = "https://api.openai.com/v1"

type conversation struct {
	ID        string         `json:"id"`
	Object    string         `json:"object,omitempty"`
	CreatedAt float64        `json:"created_at,omitempty"`
	Metadata  map[string]any `json:"metadata,omitempty"`
}

type createItemReq struct {
	Items []convItem `json:"items"`
}

type createItemResp struct {
	Object  string     `json:"object"`
	Data    []convItem `json:"data"`
	FirstID string     `json:"first_id,omitempty"`
	LastID  string     `json:"last_id,omitempty"`
	HasMore bool       `json:"has_more,omitempty"`
}

type getResponseReq struct {
	Model        string `json:"model"`
	Conversation string `json:"conversation"`
	Input        []any  `json:"input"`
}

type getResponseResp struct {
	ID         string         `json:"id"`
	Object     string         `json:"object,omitempty"`
	Model      string         `json:"model,omitempty"`
	Status     string         `json:"status,omitempty"`
	Output     []convItem     `json:"output,omitempty"`
	OutputText string         `json:"output_text,omitempty"`
	Usage      map[string]any `json:"usage,omitempty"`
	Error      *struct {
		Message string `json:"message,omitempty"`
		Type    string `json:"type,omitempty"`
	} `json:"error,omitempty"`
}

type convItem struct {
	ID      string    `json:"id,omitempty"`
	Type    string    `json:"type"`
	Status  string    `json:"status,omitempty"`
	Role    string    `json:"role"`
	Content []content `json:"content"`
}

type content struct {
	Type   string `json:"type"`
	Text   string `json:"text,omitempty"`
	FileID string `json:"file_id,omitempty"`
}

func CreateConversation(apiKey string) (string, error) {
	url := baseURL + "/conversations"

	openAIRequest := map[string]any{}

	headers := map[string]string{"Authorization": "Bearer " + apiKey, "Content-Type": "application/json"}
	var buf bytes.Buffer

	if err := json.NewEncoder(&buf).Encode(openAIRequest); err != nil {
		logger.Print(logger.ERROR, "Got error encoding request body", err)
		return "", err
	}

	resp, err := transport.SendRequest("POST", url, nil, headers, &buf)
	if err != nil {
		return "", err
	}

	var conv conversation
	if err := json.Unmarshal(resp, &conv); err != nil {
		logger.Print(logger.ERROR, "Unmarshal failed", err)
		return "", err
	}

	if len(conv.ID) > 0 {
		return conv.ID, nil
	}

	return "", errors.New("create conversation: empty id")
}

func AskAndRespond(apiKey, conversationID, model, role string, texts, files []string) (string, error) {

	var contents []content

	for _, text := range texts {
		contents = append(contents, content{Type: "input_text", Text: text})
	}

	for _, file := range files {

		fileID, err := uploadFile(apiKey, file)
		if err != nil {
			continue
		}

		contents = append(contents, content{Type: "input_file", FileID: fileID})
	}

	if err := addConversationItems(apiKey, conversationID, role, contents); err != nil {
		return "", err
	}

	return getResponseForConversation(apiKey, conversationID, model)
}

func Ask(apiKey, conversationID, model, role string, texts, files []string) error {

	var contents []content

	for _, text := range texts {
		contents = append(contents, content{Type: "input_text", Text: text})
	}

	for _, file := range files {

		fileID, err := uploadFile(apiKey, file)
		if err != nil {
			continue
		}

		contents = append(contents, content{Type: "input_file", FileID: fileID})
	}

	if err := addConversationItems(apiKey, conversationID, role, contents); err != nil {
		return err
	}

	return nil
}

func addConversationItems(apiKey, conversationID, role string, contents []content) error {
	url := fmt.Sprintf("%s/conversations/%s/items", baseURL, conversationID)

	var createItemsRequest createItemReq

	item := convItem{
		Type:    "message",
		Role:    role,
		Content: contents,
	}

	createItemsRequest.Items = append(createItemsRequest.Items, item)

	headers := map[string]string{"Authorization": "Bearer " + apiKey, "Content-Type": "application/json"}
	var buf bytes.Buffer

	if err := json.NewEncoder(&buf).Encode(createItemsRequest); err != nil {
		logger.Print(logger.ERROR, "Got error encoding request body", err)
		return err
	}

	resp, err := transport.SendRequest("POST", url, nil, headers, &buf)
	if err != nil {
		return err
	}

	var itemResp createItemResp
	if err := json.Unmarshal(resp, &itemResp); err != nil {
		logger.Print(logger.ERROR, "Unmarshal failed", err)
		return err
	}

	for _, itemData := range itemResp.Data {
		if len(itemData.ID) > 0 {
			return nil
		}
	}

	return errors.New("no item id or content returned")
}

func getResponseForConversation(apiKey, conversationID, model string) (string, error) {

	url := baseURL + "/responses"

	req := getResponseReq{
		Model:        model,
		Conversation: conversationID,
		Input:        []any{},
	}

	headers := map[string]string{"Authorization": "Bearer " + apiKey, "Content-Type": "application/json"}
	var buf bytes.Buffer

	if err := json.NewEncoder(&buf).Encode(req); err != nil {
		logger.Print(logger.ERROR, "Got error encoding responses.create req", err)
		return "", err
	}

	resp, err := transport.SendRequest("POST", url, nil, headers, &buf)
	if err != nil {
		return "", err
	}

	var responseResp getResponseResp
	if err := json.Unmarshal(resp, &responseResp); err != nil {
		logger.Print(logger.ERROR, "Unmarshal failed", err)
		return "", err
	}

	if responseResp.Error != nil {
		return "", fmt.Errorf("responses.create error: %s (%s)", responseResp.Error.Message, responseResp.Error.Type)
	}

	// Prefer output_text if present
	if len(responseResp.OutputText) > 0 {
		return responseResp.OutputText, nil
	}

	// Otherwise concatenate any output_text parts found in the output array
	var parts []string
	for _, out := range responseResp.Output {
		for _, content := range out.Content {
			if content.Type == "output_text" {
				parts = append(parts, content.Text)
			}
		}
	}

	if len(parts) > 0 {
		return strings.Join(parts, "\n"), nil
	}

	return "", errors.New("no assistant reply found in responses.create result")
}
