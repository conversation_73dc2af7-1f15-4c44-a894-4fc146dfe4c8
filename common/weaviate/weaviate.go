package weaviate

import (
	"bytes"
	"encoding/json"

	"github.com/precize/logger"
	"github.com/precize/transport"
)

type Filter struct {
	Path     string `json:"path"`
	Operator string `json:"operator"`
	Value    string `json:"value"`
}

type QuerySearchReq struct {
	ClassName string   `json:"className"`
	Filters   []Filter `json:"filters"`
	Fields    []string `json:"fields"`
	Limit     int      `json:"limit"`
}

type SimilarSearchReq struct {
	ClassName         string   `json:"className"`
	Query             string   `json:"query"`
	SearchType        string   `json:"searchType"`
	Fields            []string `json:"fields"`
	Regulator         float32  `json:"regulator"`
	Limit             int      `json:"limit"`
	FusionType        string   `json:"fusionType"`
	MaxVectorDistance float32  `json:"maxVectorDistance"`
}

func Insert(className string, objects any) error {

	reqBody := map[string]any{
		"className": className,
		"objects":   objects,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "<PERSON> failed", err)
		return err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/data/insert",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Weaviate insert successful", string(resp))
	return nil
}

func InsertWithID(className string, objects any) error {
	reqBody := map[string]any{
		"className": className,
		"objects":   objects,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/data/insertById",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Weaviate insert successful", string(resp))
	return nil
}

func SearchByQuery(className string, filters []Filter, fields []string, limit int) ([]byte, error) {
	reqBody := QuerySearchReq{
		ClassName: className,
		Filters:   filters,
		Fields:    fields,
		Limit:     limit,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return nil, err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/search/query",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func SearchSimilar(className, query, searchType, fusionType string, fields []string, regulator, maxVectorDistance float32, limit int) ([]byte, error) {
	reqBody := SimilarSearchReq{
		ClassName:         className,
		Query:             query,
		SearchType:        searchType,
		Fields:            fields,
		Regulator:         regulator,
		Limit:             limit,
		FusionType:        fusionType,
		MaxVectorDistance: maxVectorDistance,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return nil, err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/search/similar",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
