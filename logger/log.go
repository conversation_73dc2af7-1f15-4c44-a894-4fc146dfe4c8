package logger

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/precize/email"
)

const (
	INFO  = "info"
	ERROR = "error"
	DEBUG = "debug"

	maxLogSize    = 1000 * 1024 * 1024
	maxLogFiles   = 10 // Maximum number of log files to keep
	maxErrorCount = 10
)

var (
	debugMode bool

	logFileName, logFilePath, exPath string
	mu                               sync.Mutex

	errorCount                 int
	errorEmailMessage          []string
	errorThresholdEmailEnabled bool
	lastErrorEmail             time.Time
)

func init() {
	errorThresholdEmailEnabled = true
	lastErrorEmail = time.Now().UTC()
}

func InitializeLogs(serviceName string, debug bool) {

	ex, _ := os.Executable()
	exPath = filepath.Dir(ex)

	switch serviceName {
	case "provider":
		logFileName = "provider.log"
	case "enhancer":
		logFileName = "enhancer.log"
	case "prioritiser":
		logFileName = "prioritiser.log"
	case "externaldc":
		logFileName = "externaldc.log"
	case "pserver":
		logFileName = "pserver.log"
	case "analyzer":
		logFileName = "analyzer.log"
	case "vader-m":
		logFileName = "vader-m.log"
	case "vader-d":
		logFileName = "vader-d.log"
	case "migration":
		logFileName = "migration.log"
	case "context-evaluator":
		logFileName = "context-evaluator.log"
	case "contextor":
		logFileName = "contextor.log"
	}

	logFilePath = exPath + "/" + logFileName

	debugMode = debug

	if debugMode {
		Print(DEBUG, "****DEBUG MODE****")
	}
}

func Print(level, message string, extra ...any) {

	if level == DEBUG && !debugMode {
		return
	}

	mu.Lock()
	defer mu.Unlock()

	goroutineID := getGoroutineID()
	fileInfo, err := os.Stat(logFilePath)
	if err == nil && fileInfo.Size() > maxLogSize {
		rotateLogs()
	}

	file, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Fatalf("error opening log file: %v", err)
	}
	defer file.Close()

	logger := log.New(file, "", log.LstdFlags)

	for i, v := range extra {
		if context, ok := v.([]string); ok && i == 0 {
			message = "[" + strings.Join(context, "][") + "] " + message
			continue
		}
		message = message + fmt.Sprintf(" - %+v", v)
	}

	if goroutineID == 1 {
		// no goroutines available runnning on main thread
		message = message + " - " + getRSS()
	} else {
		message = fmt.Sprintf("[%d] %s - %s", goroutineID, message, getRSS())
	}

	if level == ERROR {
		message = message + "\n" + string(debug.Stack())
		LogEmailProcessor(message, false)
	}

	logger.Printf("%s", message)
}

func rotateLogs() {

	archiveDirPath := exPath + "/archive_logs"

	err := os.MkdirAll(archiveDirPath, os.ModePerm)
	if err != nil {
		log.Fatalf("error creating archive directory: %v", err)
	}

	oldestLog := filepath.Join(archiveDirPath, fmt.Sprintf("%s.%d", logFileName, maxLogFiles))
	if _, err := os.Stat(oldestLog); err == nil {
		err = os.Remove(oldestLog)
		if err != nil {
			log.Fatalf("error removing old log file: %v", err)
		}
	}

	for i := maxLogFiles - 1; i > 0; i-- {
		source := filepath.Join(archiveDirPath, fmt.Sprintf("%s.%d", logFileName, i))
		destination := filepath.Join(archiveDirPath, fmt.Sprintf("%s.%d", logFileName, i+1))

		if _, err := os.Stat(source); err == nil {
			err = os.Rename(source, destination)
			if err != nil {
				log.Fatalf("error renaming log file: %v", err)
			}
		}
	}

	err = os.Rename(logFilePath, filepath.Join(archiveDirPath, fmt.Sprintf("%s.1", logFileName)))
	if err != nil {
		log.Fatalf("error renaming log file: %v", err)
	}
}

func LogEmailProcessor(message string, force bool) {

	if force {

		if errorCount > 0 {
			if errorCount > maxErrorCount {
				errorEmailMessage = errorEmailMessage[:maxErrorCount] // In case there is more, limit it to max}
			}
			email.SendErrorEmail(logFileName, "Errors found", errorEmailMessage)
		}

		return
	}

	if strings.Contains(message, "18090: connect: connection refused") || strings.Contains(message, "18090: read: connection reset by peer") {
		// Server could be down due to deployment; Dont send emails for those
		return
	}

	errorCount++
	errorEmailMessage = append(errorEmailMessage, message)

	if errorCount > maxErrorCount {

		errorEmailMessage = errorEmailMessage[:maxErrorCount] // In case there is more, limit it to max

		if errorThresholdEmailEnabled {
			email.SendErrorEmail(logFileName, "Error threshold reached", errorEmailMessage)
			errorThresholdEmailEnabled = false

			errorEmailMessage = []string{}
			errorCount = 0
			lastErrorEmail = time.Now().UTC()
		}
	}

	if time.Now().UTC().Sub(lastErrorEmail) > 12*time.Hour {

		email.SendErrorEmail(logFileName, "Errors found", errorEmailMessage)

		errorEmailMessage = []string{}
		errorCount = 0
		lastErrorEmail = time.Now().UTC()
	}
}

func getRSS() (rssString string) {

	data, err := os.ReadFile("/proc/self/status")
	if err != nil {
		return ""
	}

	for _, line := range strings.Split(string(data), "\n") {
		if strings.HasPrefix(line, "VmRSS:") {
			fields := strings.Fields(line)
			rssKB, _ := strconv.Atoi(fields[1])
			return strconv.Itoa(rssKB) + " KB"
		}
	}
	return ""
}

func getGoroutineID() uint64 {

	b := make([]byte, 64)
	b = b[:runtime.Stack(b, false)]

	s := string(b)
	s = strings.TrimPrefix(s, "goroutine ")
	s = s[:strings.IndexByte(s, ' ')]

	id, _ := strconv.ParseUint(s, 10, 64)
	return id
}
