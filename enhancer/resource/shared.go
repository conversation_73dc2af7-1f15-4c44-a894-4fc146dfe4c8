package resource

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/enhancer/activity"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/source/code"
	"github.com/precize/enhancer/source/issue"
	"github.com/precize/logger"
)

func ProcessBatchContext(resourceContext *rcontext.ResourceContext, contextDocIDs []string) {
	if err := activity.
		GetActivityContextOfResource(resourceContext, contextDocIDs); err != nil {
		logger.Print(logger.ERROR, "Failed to get activity context", []string{err.Error()})
		return
	}

	if err := code.GetCodeContextOfResource(resourceContext, contextDocIDs); err != nil {
		logger.Print(logger.ERROR, "Failed to get code context", []string{err.Error()})
		return
	}

	if err := issue.GetJiraContextOfResource(resourceContext, contextDocIDs); err != nil {
		logger.Print(logger.ERROR, "Failed to get Jira context", []string{err.Error()})
		return
	}
}

func IsDefaultResource(resourceType, resourceID string, entityJSON map[string]any) (defaultResource bool) {

	switch resourceType {

	case common.AWS_ROOTUSER_RESOURCE_TYPE:
		defaultResource = true
	case common.AWS_S3_RESOURCE_TYPE:
		if strings.HasPrefix(strings.ToLower(resourceID), "cf-templates") {
			defaultResource = true
		}
	default:
		defaultResource, _ = entityJSON["isDefault"].(bool)
	}

	return
}
