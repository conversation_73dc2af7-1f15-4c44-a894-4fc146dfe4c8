package main

import (
	"encoding/json"

	"github.com/precize/common/weaviate"
	"github.com/precize/logger"
)

const BREADCRUMB_CLASS = "BreadcrumbsV2"

type BreadcrumbRecord struct {
	ID          string `json:"id"`
	EntityID    string `json:"entityId,omitempty"`
	Breadcrumbs string `json:"breadcrumbs"`
	TenantID    string `json:"tenantId,omitempty"`
	ServiceID   string `json:"serviceId,omitempty"`
	Source      string `json:"source,omitempty"`

	// Only used for API specific responses
	Additional *struct {
		Score        string `json:"score"`
		ExplainScore string `json:"explainScore"`
	} `json:"_additional,omitempty"`
}

type BreadcrumbSearchResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Data struct {
			Get struct {
				Breadcrumbs []BreadcrumbRecord `json:"BreadcrumbsV2"`
			} `json:"Get"`
		} `json:"data"`
	} `json:"data"`
}

func InsertBreadcrumb(records []BreadcrumbRecord) error {
	return weaviate.InsertWithID(BREADCRUMB_CLASS, records)
}

func SearchSimilarBreadcrumbs(query string) ([]BreadcrumbRecord, error) {

	resp, err := weaviate.SearchSimilar(BREADCRUMB_CLASS, query, "hybrid", "relativeScoreFusion", []string{"entityId", "breadcrumbs"}, 1, 0.7, 10)
	if err != nil {
		return nil, err
	}

	var bcSearchResp BreadcrumbSearchResponse
	if err := json.Unmarshal(resp, &bcSearchResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal Breadcrumbs search response", err)
		return nil, err
	}

	if !bcSearchResp.Success {
		return nil, nil
	}

	logger.Print(logger.INFO, "Weaviate Breadcrumbs similar search successful for "+query)
	return bcSearchResp.Data.Data.Get.Breadcrumbs, nil
}

func SearchBreadcrumbByEntityID(entityID string) (*BreadcrumbRecord, error) {

	filters := []weaviate.Filter{
		{
			Path:     "entityId",
			Operator: "Equal",
			Value:    entityID,
		},
	}

	resp, err := weaviate.SearchByQuery(BREADCRUMB_CLASS, filters, []string{"entityId", "breadcrumbs"}, 1)
	if err != nil {
		return nil, err
	}

	var bcSearchResp BreadcrumbSearchResponse
	if err := json.Unmarshal(resp, &bcSearchResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal Breadcrumbs query response", err)
		return nil, err
	}

	if !bcSearchResp.Success {
		return nil, nil
	}

	if len(bcSearchResp.Data.Data.Get.Breadcrumbs) > 0 {
		logger.Print(logger.INFO, "Weaviate Breadcrumbs search by entityId successful", bcSearchResp.Message)
		return &bcSearchResp.Data.Data.Get.Breadcrumbs[0], nil
	}

	return nil, nil
}
