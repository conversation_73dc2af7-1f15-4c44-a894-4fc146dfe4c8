package main

import (
	"encoding/json"
	// "strconv"

	"github.com/precize/common"
	"github.com/precize/common/basic/uuid"
	"github.com/precize/logger"
)

const (
	MAX_RECORDS = 100
)

func DeriveContext(aiClient common.OpenAIClient, tenantID, lastCollectedAt, serviceID string) error {

	ce := ContextEngine{
		AI:          aiClient,
		TenantID:    tenantID,
		CollectedAt: lastCollectedAt,
		ServiceID:   serviceID,
	}

	err := ce.InitializeSystemPrompt()
	if err != nil {
		return err
	}

	var searchAfter any

	for {

		logger.Print(logger.INFO, "Collecting data", []string{tenantID})

		resourcesDocs, sortResponse, err := ce.CollectResourceData(searchAfter)
		if err != nil {
			break
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		rscDocs := []map[string]any{}

		for _, resourcesDoc := range resourcesDocs {

			if entityID, ok := resourcesDoc["entityId"].(string); ok {

				extractionRequest, err := createExtractionRequest(resourcesDoc)
				if err != nil {
					continue
				}

				logger.Print(logger.INFO, "Extracting data", []string{tenantID}, entityID, extractionRequest)

				breadcrumbs, err := ce.ExtractBreadcrumbs(extractionRequest)
				if err != nil {
					continue
				}

				var breadcrumbMap = make(map[string][]string)

				for _, breadcrumb := range breadcrumbs {
					breadcrumbMap[breadcrumb.Type] = append(breadcrumbMap[breadcrumb.Type], breadcrumb.Value)
				}

				bcBytes, err := json.Marshal(breadcrumbMap)
				if err != nil {
					logger.Print(logger.ERROR, "Marshal error", err)
					continue
				}

				entityType, _ := resourcesDoc["entityType"].(string)

				id := uuid.GenerateUUIDFromString(entityID + entityType + ce.TenantID)

				bcRecord := BreadcrumbRecord{
					EntityID:    entityID,
					Breadcrumbs: string(bcBytes),
					TenantID:    ce.TenantID,
					ServiceID:   ce.ServiceID,
					Source:      "json",
					ID:          id,
				}

				logger.Print(logger.INFO, "Inserting breadcrumbs", []string{tenantID}, bcRecord, entityID)

				if err = ce.InsertBreadcrumb([]BreadcrumbRecord{bcRecord}); err != nil {
					continue
				}

				rscDocs = append(rscDocs, resourcesDoc)

				if len(rscDocs) >= MAX_RECORDS {
					ce.VectorizeResourceMetadata(rscDocs)
					rscDocs = []map[string]any{}
				}
			}
		}

		if len(rscDocs) > 0 {
			ce.VectorizeResourceMetadata(rscDocs)
		}
	}

	searchAfter = nil

	for {

		resourcesDocs, sortResponse, err := ce.CollectResourceData(searchAfter)
		if err != nil {
			break
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, resourcesDoc := range resourcesDocs {

			primaryEntityID, ok := resourcesDoc["entityId"].(string)

			if ok {

				logger.Print(logger.INFO, "Getting breadcrumbs of entityId", []string{tenantID}, primaryEntityID)

				primaryRecord, err := ce.SearchBreadcrumbByEntityID(primaryEntityID)
				if err != nil {
					continue
				}

				logger.Print(logger.INFO, "Got breadcrumbs", []string{tenantID}, *primaryRecord, primaryEntityID)

				if len(primaryRecord.EntityID) > 0 {

					candidateRecords, err := ce.SearchSimilarBreadcrumbs(primaryRecord.Breadcrumbs)
					if err != nil {
						continue
					}

					var candidatesBreadcrumbMap = make(map[string]string)

					for _, candidateRecord := range candidateRecords {
						if candidateRecord.EntityID == primaryEntityID {
							continue
						}

						// score, err := strconv.ParseFloat(candidateRecord.Additional.Score, 64)
						// if err == nil && score < 0.5 {
						// 	continue
						// }

						candidatesBreadcrumbMap[candidateRecord.EntityID] = candidateRecord.Breadcrumbs
					}

					candidatesBreadcrumbs, err := json.Marshal(candidatesBreadcrumbMap)
					if err != nil {
						logger.Print(logger.ERROR, "Marshal error", err)
						continue
					}

					validateResp, err := ce.ValidateContext(primaryRecord.Breadcrumbs, string(candidatesBreadcrumbs))
					if err != nil {
						continue
					}

					logger.Print(logger.INFO, "Final", *validateResp, primaryEntityID)
				}
			}
		}
	}

	return nil
}
