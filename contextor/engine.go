package main

import (
	"encoding/json"
	"fmt"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	elasticutils "github.com/precize/common/elastic"
	"github.com/precize/common/openai"
	rscmetadata "github.com/precize/contextor/rsc_metadata"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type CompanyContext struct {
	CompanyName   string   `json:"company_name"`
	Domains       []string `json:"domains"`
	Applications  []string `json:"applications"`
	Teams         []string `json:"teams"`
	Technologies  []string `json:"technologies"`
	BusinessModel string   `json:"business_model"`
	Notes         string   `json:"notes"`
}

type Breadcrumb struct {
	Value      string  `json:"value"`
	Type       string  `json:"type,omitempty"`
	Confidence float64 `json:"confidence,omitempty"`
}

type ContextEngine struct {
	AI           common.OpenAIClient
	TenantID     string
	CollectedAt  string
	ServiceID    string
	SystemPrompt string
}

func (ce *ContextEngine) InitializeSystemPrompt() error {

	companyName, err := contextutils.GetCompanyName(ce.TenantID)
	if err != nil {
		return err
	}

	convID, err := openai.CreateConversation(ce.AI.APIKey)
	if err != nil {
		return err
	}

	out, err := openai.AskAndRespond(ce.AI.APIKey, convID, "gpt-4o-mini", "user", []string{fmt.Sprintf(CUSTOMER_PROMPT, companyName)}, nil)
	if err != nil {
		return err
	}

	companyContext, err := parseCompanyContext(out)
	if err != nil {
		return err
	}

	companyContextJSON, err := json.Marshal(companyContext)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal error", err)
		return err
	}

	logger.Print(logger.INFO, "Company context", string(companyContextJSON))

	// TODO: Add global apps/teams into system prompt

	ce.SystemPrompt = fmt.Sprintf(INTRO_PROMPT + "\n\nAdditional customer-specific context:\n" + string(companyContextJSON) + "\n\nUse this background knowledge ONLY to understand what this company does and to interpret resources more accurately, focusing on how the customers data and context go hand in hand. DO NOT use this data to extract breadcrumbs or derive context for the resources.")

	return nil
}

func (ce *ContextEngine) CollectResourceData(searchAfter any) (map[string]map[string]any, any, error) {

	resourcesQuery := `{"_source":["entityId","serviceId","entityType","entityJson","tags"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + ce.TenantID + `"}},{"term":{"collectedAt":` + ce.CollectedAt + `}},{"term":{"serviceId":` + ce.ServiceID + `}}],"must_not":[{"terms":{"entityType.keyword":[` + elasticutils.GetNonDataResourcesElasticQuery() + `]}}]}}}`

	resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, resourcesQuery, searchAfter)
	if err != nil {
		return nil, nil, err
	}

	if len(resourcesDocs) > 0 {
		return resourcesDocs, sortResponse, nil
	} else {
		return nil, nil, nil
	}
}

func (ce *ContextEngine) ExtractBreadcrumbs(extractionRequest string) ([]Breadcrumb, error) {

	convID, err := openai.CreateConversation(ce.AI.APIKey)
	if err != nil {
		return nil, err
	}

	err = openai.Ask(ce.AI.APIKey, convID, "gpt-4o-mini", "system", []string{ce.SystemPrompt}, nil)
	if err != nil {
		return nil, err
	}

	out, err := openai.AskAndRespond(ce.AI.APIKey, convID, "gpt-4o-mini", "user", []string{fmt.Sprintf(EXTRACTION_PROMPT, extractionRequest)}, nil)
	if err != nil {
		return nil, err
	}

	openai.RemoveJSONQuoteFromResponse(&out)

	var bcResp struct {
		Breadcrumbs []Breadcrumb `json:"breadcrumbs"`
	}

	if err := json.Unmarshal([]byte(out), &bcResp); err != nil {
		logger.Print(logger.ERROR, "Unmarshal error", err, out)
		return nil, err
	}

	logger.Print(logger.INFO, "Breadcrumbs", bcResp.Breadcrumbs)

	return bcResp.Breadcrumbs, nil
}

type ValidateResp struct {
	ContextualMatches []struct {
		ResourceID string  `json:"resourceId"`
		Reason     string  `json:"reason"`
		Score      float64 `json:"score"`
	} `json:"contextual_matches"`
}

func (ce *ContextEngine) ValidateContext(primaryRecord, candidateRecords string) (*ValidateResp, error) {

	convID, err := openai.CreateConversation(ce.AI.APIKey)
	if err != nil {
		return nil, err
	}

	out, err := openai.AskAndRespond(ce.AI.APIKey, convID, "gpt-4o-mini", "user", []string{fmt.Sprintf(VALIDATION_PROMPT, primaryRecord, candidateRecords)}, nil)
	if err != nil {
		return nil, err
	}

	openai.RemoveJSONQuoteFromResponse(&out)

	var validateResp ValidateResp

	if err := json.Unmarshal([]byte(out), &validateResp); err != nil {
		logger.Print(logger.ERROR, "Unmarshal error", err, out)
		return nil, err
	}

	return &validateResp, nil
}

func (ce *ContextEngine) InsertBreadcrumb(breadcrumbs []BreadcrumbRecord) error {
	return InsertBreadcrumb(breadcrumbs)
}

func (ce *ContextEngine) SearchBreadcrumbByEntityID(entityID string) (*BreadcrumbRecord, error) {
	return SearchBreadcrumbByEntityID(entityID)
}

func (ce *ContextEngine) SearchSimilarBreadcrumbs(breadcrumbs string) ([]BreadcrumbRecord, error) {
	return SearchSimilarBreadcrumbs(breadcrumbs)
}

func (ce *ContextEngine) VectorizeResourceMetadata(resourceDocs []map[string]any) {
	rscmetadata.VectorizeResourceMetadata(resourceDocs, ce.TenantID, ce.CollectedAt, ce.ServiceID)
}

func (ce *ContextEngine) SearchSimilarResourceUsingRscMetadata(rscMetadata string) ([]rscmetadata.ResourceMetadata, error) {
	return rscmetadata.SearchSimilarResourceUsingRscMetadata(rscMetadata)
}
