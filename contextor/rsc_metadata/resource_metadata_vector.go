package rscmetadata

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/common/basic/uuid"
	"github.com/precize/common/weaviate"
	"github.com/precize/logger"
	"github.com/precize/provider/wiz/types"
)

const (
	RESOURCE_METADATA_CLASS = "ResourceMetadata"
	MAX_RECORDS             = 100
)

func VectorizeResourceMetadata(resourceDoc []map[string]any, tenantID, collectedAt, serviceID string) {

	var (
		records []ResourceMetadata
	)

	for _, resourceDoc := range resourceDoc {

		rscDocBytes, err := json.Marshal(resourceDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
			return
		}

		var crDoc types.CloudResource
		if err = json.Unmarshal(rscDocBytes, &crDoc); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
			return
		}

		var tagParts []string
		for _, t := range crDoc.Tags {
			tagParts = append(tagParts, fmt.Sprintf("%s:%s", t.Key, t.Value))
		}
		tagString := strings.Join(tagParts, ", ")

		id := uuid.GenerateUUIDFromString(crDoc.EntityID + crDoc.EntityType + crDoc.TenantID)
		description := fmt.Sprintf("name: %s | tags: %s", crDoc.ResourceName, tagString)

		vectorInsertDoc := ResourceMetadata{
			ID:          id,
			EntityID:    crDoc.EntityID,
			EntityName:  crDoc.ResourceName,
			TenantID:    crDoc.TenantID,
			ServiceID:   crDoc.ServiceID,
			Description: description,
		}

		records = append(records, vectorInsertDoc)
	}

	if err := InsertResourceMetadata(records); err != nil {
		logger.Print(logger.ERROR, "Error inserting resource metadata", []string{tenantID}, err)
		return
	}
}

func InsertResourceMetadata(records []ResourceMetadata) error {
	return weaviate.InsertWithID(RESOURCE_METADATA_CLASS, records)
}

func SearchSimilarResourceUsingRscMetadata(rscMetadata string) ([]ResourceMetadata, error) {

	//TODO
	return nil, nil
}
