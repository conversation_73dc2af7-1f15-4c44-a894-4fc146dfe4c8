package main

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/common"
	cloudutils "github.com/precize/common/cloud"
	"github.com/precize/common/openai"
	"github.com/precize/logger"
)

func buildESQueryFromBreadcrumbs(bcs []Breadcrumb, entityID, tenantID, lastCollectedAt, serviceID string) map[string]any {
	should := make([]any, 0, len(bcs))
	for _, bc := range bcs {
		if strings.TrimSpace(bc.Value) == "" {
			continue
		}
		should = append(should, map[string]any{
			"match_phrase": map[string]any{
				"entityJson": bc.Value,
			},
		})
	}

	must := make([]any, 0, 3)

	must = append(must, []any{
		map[string]any{
			"term": map[string]any{
				"tenantId.keyword": tenantID,
			},
		},
		map[string]any{
			"term": map[string]any{
				"collectedAt": lastCollectedAt,
			},
		},
		map[string]any{
			"term": map[string]any{
				"serviceId": serviceID,
			},
		},
	}...)

	mustNot := make([]any, 0, 1)

	mustNot = append(mustNot, map[string]any{
		"term": map[string]any{
			"entityId.keyword": entityID,
		},
	})

	query := map[string]any{
		"_source": []string{"entityId", "entityType", "entityJson"},
		"query": map[string]any{
			"bool": map[string]any{
				"should":   should,
				"must":     must,
				"must_not": mustNot,
			},
		},
	}
	return query
}

func parseCompanyContext(aiResponse string) (*CompanyContext, error) {

	var ctx CompanyContext

	openai.RemoveJSONQuoteFromResponse(&aiResponse)

	var raw map[string]any
	if err := json.Unmarshal([]byte(aiResponse), &raw); err != nil {
		return nil, fmt.Errorf("invalid JSON: %w", err)
	}

	ctx.CompanyName, _ = raw["company_name"].(string)
	ctx.BusinessModel, _ = raw["business_model"].(string)
	ctx.Notes, _ = raw["notes"].(string)

	ctx.Domains = coerceStringSlice(raw["domains"])
	ctx.Applications = coerceStringSlice(raw["applications"])
	ctx.Teams = coerceStringSlice(raw["teams"])
	ctx.Technologies = coerceStringSlice(raw["technologies"])

	return &ctx, nil
}

func coerceStringSlice(val any) []string {
	arr, ok := val.([]any)
	if !ok {
		return nil
	}
	result := make([]string, 0, len(arr))
	for _, v := range arr {
		if s, ok := v.(string); ok {
			result = append(result, s)
		}
	}
	return result
}

type extractionRequest struct {
	ResourceName string             `json:"resourceName"`
	Description  string             `json:"description"`
	Tags         []common.TagObject `json:"tags"`
}

func createExtractionRequest(resourcesDoc map[string]any) (string, error) {

	var (
		extractionReq        extractionRequest
		entityJSONMap        = make(map[string]any)
		resourceNameTagValue string
	)

	entityID, _ := resourcesDoc["entityId"].(string)
	entityType, _ := resourcesDoc["entityType"].(string)
	serviceIDFloat, _ := resourcesDoc["serviceId"].(float64)
	entityJSON, _ := resourcesDoc["entityJson"].(string)
	tags, _ := resourcesDoc["tags"].([]any)

	err := json.Unmarshal([]byte(entityJSON), &entityJSONMap)
	if err != nil {
		logger.Print(logger.ERROR, "Unmarshal error", err)
		return "", err
	}

	extractionReq.Description, _ = entityJSONMap["description"].(string)

	for _, tag := range tags {
		if tagMap, ok := tag.(map[string]any); ok {
			k, _ := tagMap["key"].(string)
			v, _ := tagMap["value"].(string)

			if k == "Name" {
				resourceNameTagValue = v
			}

			extractionReq.Tags = append(extractionReq.Tags, common.TagObject{Key: k, Value: v})
		}
	}

	switch int(serviceIDFloat) {
	case common.AWS_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForAWSResource(entityID, entityType, resourceNameTagValue, entityJSON)
	case common.AZURE_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForAzureResource(entityID, entityType, entityJSON)
	case common.GCP_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForGCPResource(entityID, entityType, entityJSON)
	case common.OPENAI_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForOpenAIResource(entityID, entityJSON)
	default:
		if name := cloudutils.GetResourceNameFromEntityJSON(entityJSON); len(name) > 0 {
			extractionReq.ResourceName = name
		}
	}

	extractionJSON, err := json.Marshal(extractionReq)
	if err != nil {
		return "", err
	}

	return string(extractionJSON), err
}
